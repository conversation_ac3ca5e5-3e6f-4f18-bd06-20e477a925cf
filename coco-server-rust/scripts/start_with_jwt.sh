#!/bin/bash

# Coco Server Rust 启动脚本（带JWT密钥配置）
# 用法: ./scripts/start_with_jwt.sh [jwt_secret]

set -e

echo "🚀 启动 Coco Server Rust..."

# 检查是否提供了JWT密钥参数
if [ $# -eq 1 ]; then
    JWT_SECRET="$1"
    echo "📝 使用提供的JWT密钥"
elif [ -n "$JWT_SECRET" ]; then
    echo "📝 使用环境变量中的JWT密钥"
elif [ -f ".env" ] && grep -q "JWT_SECRET=" .env; then
    echo "📝 使用 .env 文件中的JWT密钥"
    source .env
else
    echo "❌ 错误: 未找到JWT密钥配置"
    echo ""
    echo "请使用以下方式之一设置JWT密钥:"
    echo "1. 作为参数传递: ./scripts/start_with_jwt.sh 'your-secret-key'"
    echo "2. 设置环境变量: export JWT_SECRET='your-secret-key'"
    echo "3. 创建 .env 文件: echo 'JWT_SECRET=your-secret-key' > .env"
    echo ""
    echo "💡 提示: 使用 ./scripts/generate_jwt_secret.sh 生成安全的密钥"
    exit 1
fi

# 验证JWT密钥长度
if [ ${#JWT_SECRET} -lt 16 ]; then
    echo "⚠️  警告: JWT密钥长度少于16字符，建议使用更长的密钥"
fi

# 导出JWT密钥环境变量
export JWT_SECRET

echo "✅ JWT密钥配置完成"
echo "🔧 编译项目..."

# 编译项目
cargo build --release

echo "🎯 启动服务器..."

# 启动服务器
cargo run --release
