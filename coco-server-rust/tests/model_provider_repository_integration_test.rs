/// 集成测试：ModelProvider Repository功能
///
/// 这个测试验证TASK-003的完整功能：
/// 1. SurrealDB连接正常
/// 2. Repository CRUD操作正常
/// 3. 搜索功能正常
/// 4. 业务规则保护正常
use std::sync::Arc;

use coco_server::{
    database::{DatabaseConfig, SurrealDBClient},
    models::model_provider::{ModelProvider, ModelSettings},
    repositories::model_provider_repo::{
        ModelProviderRepository, SearchQuery, SurrealModelProviderRepository,
    },
};

/// 创建测试用的ModelProvider
fn create_test_model_provider(name: &str, api_type: &str) -> ModelProvider {
    ModelProvider {
        id: None,
        created: chrono::Utc::now(),
        updated: chrono::Utc::now(),
        name: name.to_string(),
        api_type: api_type.to_string(),
        base_url: "https://api.example.com".to_string(),
        api_key: "test-key".to_string(),
        description: format!("Test provider: {}", name),
        enabled: true,
        builtin: false,
        icon: "".to_string(),
        models: vec![],
    }
}

/// 测试Repository基础CRUD操作
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_model_provider_repository_crud() {
    // 初始化数据库连接
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());

    // 创建Repository
    let repository = SurrealModelProviderRepository::new(Arc::new(db_client.db().clone()));

    // 测试数据
    let mut test_provider = create_test_model_provider("Test Provider", "openai");

    // 1. 测试创建
    let created_id = repository.create(&test_provider).await.unwrap();
    assert!(!created_id.is_empty());

    // 2. 测试按ID查询
    let found_provider = repository.get_by_id(&created_id).await.unwrap();
    assert!(found_provider.is_some());
    let found_provider = found_provider.unwrap();
    assert_eq!(found_provider.name, test_provider.name);
    assert_eq!(found_provider.api_type, test_provider.api_type);

    // 3. 测试按名称查询
    let found_by_name = repository.find_by_name(&test_provider.name).await.unwrap();
    assert!(found_by_name.is_some());
    assert_eq!(found_by_name.unwrap().get_id_string(), created_id);

    // 4. 测试存在性检查
    let exists = repository.exists(&created_id).await.unwrap();
    assert!(exists);

    // 5. 测试更新
    test_provider.description = "Updated description".to_string();
    repository.update(&test_provider).await.unwrap();

    let updated_provider = repository.get_by_id(&created_id).await.unwrap().unwrap();
    assert_eq!(
        updated_provider.description,
        "Updated description".to_string()
    );

    // 6. 测试删除
    repository.delete(&created_id).await.unwrap();

    let deleted_provider = repository.get_by_id(&created_id).await.unwrap();
    assert!(deleted_provider.is_none());

    println!("✅ ModelProvider Repository CRUD测试通过！");
}

/// 测试搜索功能
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_model_provider_repository_search() {
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());
    let repository = SurrealModelProviderRepository::new(Arc::new(db_client.db().clone()));

    // 创建测试数据
    let provider1 = create_test_model_provider("OpenAI Provider", "openai");
    let provider2 = create_test_model_provider("DeepSeek Provider", "deepseek");
    let mut provider3 = create_test_model_provider("Disabled Provider", "custom");
    provider3.enabled = false;

    let id1 = repository.create(&provider1).await.unwrap();
    let id2 = repository.create(&provider2).await.unwrap();
    let id3 = repository.create(&provider3).await.unwrap();

    // 1. 测试基础搜索
    let query = SearchQuery::new();
    let results = repository.search(&query).await.unwrap();
    assert!(results.hits.total.value >= 3);

    // 2. 测试文本搜索
    let query = SearchQuery::new().with_query("OpenAI".to_string());
    let results = repository.search(&query).await.unwrap();
    assert!(results
        .hits
        .hits
        .iter()
        .any(|hit| hit.source.name.contains("OpenAI")));

    // 3. 测试过滤搜索
    let query =
        SearchQuery::new().with_filter("enabled".to_string(), serde_json::Value::Bool(true));
    let results = repository.search(&query).await.unwrap();
    assert!(results.hits.hits.iter().all(|hit| hit.source.enabled));

    // 4. 测试分页
    let query = SearchQuery::new().with_pagination(1, 0);
    let results = repository.search(&query).await.unwrap();
    assert!(results.hits.hits.len() <= 1);

    // 5. 测试排序
    let query = SearchQuery::new().with_sort("name:asc".to_string());
    let results = repository.search(&query).await.unwrap();
    if results.hits.hits.len() > 1 {
        let names: Vec<&str> = results
            .hits
            .hits
            .iter()
            .map(|hit| hit.source.name.as_str())
            .collect();
        let mut sorted_names = names.clone();
        sorted_names.sort();
        assert_eq!(names, sorted_names);
    }

    // 清理测试数据
    let _ = repository.delete(&id1).await;
    let _ = repository.delete(&id2).await;
    let _ = repository.delete(&id3).await;

    println!("✅ ModelProvider Repository搜索测试通过！");
}

/// 测试业务规则保护
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_model_provider_repository_business_rules() {
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());
    let repository = SurrealModelProviderRepository::new(Arc::new(db_client.db().clone()));

    // 1. 测试名称唯一性
    let provider1 = create_test_model_provider("Unique Name", "openai");
    let provider2 = create_test_model_provider("Unique Name", "deepseek");

    let id1 = repository.create(&provider1).await.unwrap();
    let result2 = repository.create(&provider2).await;
    assert!(result2.is_err());

    // 2. 测试内置提供商保护
    let mut builtin_provider = create_test_model_provider("Builtin Provider", "builtin");
    builtin_provider.builtin = true;
    let builtin_id = repository.create(&builtin_provider).await.unwrap();

    let delete_result = repository.delete(&builtin_id).await;
    assert!(delete_result.is_err());

    // 3. 测试更新时的名称冲突检查
    let provider3 = create_test_model_provider("Another Provider", "custom");
    let id3 = repository.create(&provider3).await.unwrap();

    let mut update_provider = repository.get_by_id(&id3).await.unwrap().unwrap();
    update_provider.name = "Unique Name".to_string(); // 与provider1重名
    let update_result = repository.update(&update_provider).await;
    assert!(update_result.is_err());

    // 清理测试数据
    let _ = repository.delete(&id1).await;
    let _ = repository.delete(&id3).await;
    // builtin_provider无法删除，这是正确的行为

    println!("✅ ModelProvider Repository业务规则测试通过！");
}

/// 测试错误处理
#[tokio::test]
#[ignore] // 需要运行中的SurrealDB实例
async fn test_model_provider_repository_error_handling() {
    let db_config = DatabaseConfig::default();
    let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());
    let repository = SurrealModelProviderRepository::new(Arc::new(db_client.db().clone()));

    // 1. 测试查询不存在的ID
    let non_existent_id = "non-existent-id";
    let result = repository.get_by_id(non_existent_id).await.unwrap();
    assert!(result.is_none());

    // 2. 测试删除不存在的ID
    let delete_result = repository.delete(non_existent_id).await;
    assert!(delete_result.is_err());

    // 3. 测试更新不存在的提供商
    let non_existent_provider = create_test_model_provider("Non Existent", "test");
    let update_result = repository.update(&non_existent_provider).await;
    assert!(update_result.is_err());

    println!("✅ ModelProvider Repository错误处理测试通过！");
}
