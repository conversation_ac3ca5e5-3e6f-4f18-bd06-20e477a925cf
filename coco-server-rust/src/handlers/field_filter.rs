use std::collections::HashSet;

use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::models::model_provider::{ModelConfig, ModelProvider};

/// 字段过滤器
///
/// 负责过滤敏感字段和控制响应中包含的字段
pub struct FieldFilter;

/// 过滤后的模型配置（用于搜索响应）
#[derive(Debug, Serialize, Deserialize)]
pub struct FilteredModelConfig {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settings: Option<Value>,
}

/// 过滤后的模型提供商（用于搜索响应）
#[derive(Debug, Serialize, Deserialize)]
pub struct FilteredModelProvider {
    pub id: String,
    pub created: chrono::DateTime<chrono::Utc>,
    pub updated: chrono::DateTime<chrono::Utc>,
    pub name: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<FilteredModelConfig>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
    // 注意：不包含api_key等敏感字段
}

impl FieldFilter {
    /// 敏感字段列表
    const SENSITIVE_FIELDS: &'static [&'static str] =
        &["api_key", "secret", "password", "token", "credential"];

    /// 受保护字段列表（内置提供商不能修改）
    const PROTECTED_FIELDS: &'static [&'static str] = &["builtin", "created", "id"];

    /// 过滤敏感字段
    ///
    /// # 参数
    /// * `provider` - 原始模型提供商数据
    ///
    /// # 返回
    /// 过滤后的模型提供商数据
    pub fn filter_sensitive_fields(provider: ModelProvider) -> FilteredModelProvider {
        FilteredModelProvider {
            id: provider.get_id_string(),
            created: provider.created,
            updated: provider.updated,
            name: provider.name,
            api_type: provider.api_type,
            base_url: provider.base_url,
            icon: provider.icon,
            models: Self::filter_model_configs(provider.models),
            enabled: provider.enabled,
            builtin: provider.builtin,
            description: provider.description,
        }
    }

    /// 过滤模型配置中的敏感信息
    ///
    /// # 参数
    /// * `models` - 原始模型配置列表
    ///
    /// # 返回
    /// 过滤后的模型配置列表
    fn filter_model_configs(models: Vec<ModelConfig>) -> Vec<FilteredModelConfig> {
        models
            .into_iter()
            .map(|model| FilteredModelConfig {
                name: model.name,
                settings: model.settings.and_then(|settings| {
                    // 将ModelSettings转换为JSON Value，然后过滤
                    serde_json::to_value(settings)
                        .ok()
                        .map(|value| Self::filter_json_object(value))
                }),
            })
            .collect()
    }

    /// 过滤JSON对象中的敏感字段
    ///
    /// # 参数
    /// * `value` - 原始JSON值
    ///
    /// # 返回
    /// 过滤后的JSON值
    fn filter_json_object(value: Value) -> Value {
        match value {
            Value::Object(mut map) => {
                // 移除敏感字段
                for sensitive_field in Self::SENSITIVE_FIELDS {
                    map.remove(*sensitive_field);
                }

                // 递归过滤嵌套对象
                for (_, v) in map.iter_mut() {
                    *v = Self::filter_json_object(v.clone());
                }

                Value::Object(map)
            }
            Value::Array(arr) => Value::Array(
                arr.into_iter()
                    .map(|v| Self::filter_json_object(v))
                    .collect(),
            ),
            _ => value,
        }
    }

    /// 检查字段是否为敏感字段
    ///
    /// # 参数
    /// * `field_name` - 字段名称
    ///
    /// # 返回
    /// 如果是敏感字段返回true
    pub fn is_sensitive_field(field_name: &str) -> bool {
        Self::SENSITIVE_FIELDS.contains(&field_name)
    }

    /// 检查字段是否为受保护字段
    ///
    /// # 参数
    /// * `field_name` - 字段名称
    ///
    /// # 返回
    /// 如果是受保护字段返回true
    pub fn is_protected_field(field_name: &str) -> bool {
        Self::PROTECTED_FIELDS.contains(&field_name)
    }

    /// 获取允许更新的字段列表
    ///
    /// # 参数
    /// * `is_builtin` - 是否为内置提供商
    ///
    /// # 返回
    /// 允许更新的字段集合
    pub fn get_updatable_fields(is_builtin: bool) -> HashSet<String> {
        let mut updatable_fields = HashSet::new();

        // 基础可更新字段
        updatable_fields.insert("api_key".to_string());
        updatable_fields.insert("api_type".to_string());
        updatable_fields.insert("base_url".to_string());
        updatable_fields.insert("icon".to_string());
        updatable_fields.insert("models".to_string());
        updatable_fields.insert("enabled".to_string());
        updatable_fields.insert("description".to_string());

        // 内置提供商不能修改name字段
        if !is_builtin {
            updatable_fields.insert("name".to_string());
        }

        updatable_fields
    }

    /// 验证更新字段是否被允许
    ///
    /// # 参数
    /// * `field_name` - 字段名称
    /// * `is_builtin` - 是否为内置提供商
    ///
    /// # 返回
    /// 如果字段允许更新返回true
    pub fn is_field_updatable(field_name: &str, is_builtin: bool) -> bool {
        let updatable_fields = Self::get_updatable_fields(is_builtin);
        updatable_fields.contains(field_name)
    }

    /// 过滤更新请求中的不允许字段
    ///
    /// # 参数
    /// * `update_data` - 更新数据JSON
    /// * `is_builtin` - 是否为内置提供商
    ///
    /// # 返回
    /// 过滤后的更新数据和被过滤的字段列表
    pub fn filter_update_fields(update_data: Value, is_builtin: bool) -> (Value, Vec<String>) {
        let mut filtered_fields = Vec::new();

        match update_data {
            Value::Object(mut map) => {
                let updatable_fields = Self::get_updatable_fields(is_builtin);

                // 移除不允许更新的字段
                map.retain(|key, _| {
                    if updatable_fields.contains(key) {
                        true
                    } else {
                        filtered_fields.push(key.clone());
                        false
                    }
                });

                (Value::Object(map), filtered_fields)
            }
            _ => (update_data, filtered_fields),
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;
    use serde_json::json;

    use super::*;
    use crate::models::model_provider::ModelSettings;

    fn create_test_provider() -> ModelProvider {
        ModelProvider {
            id: None,
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "secret-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![ModelConfig {
                name: "test-model".to_string(),
                settings: Some(ModelSettings {
                    temperature: Some(0.8),
                    top_p: None,
                    presence_penalty: None,
                    frequency_penalty: None,
                    max_tokens: Some(1000),
                }),
            }],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        }
    }

    #[test]
    fn test_filter_sensitive_fields() {
        let provider = create_test_provider();
        let filtered = FieldFilter::filter_sensitive_fields(provider);

        assert_eq!(filtered.name, "Test Provider");
        assert_eq!(filtered.api_type, "openai");
        assert_eq!(filtered.models.len(), 1);

        // 检查模型设置中的字段
        let model_settings = &filtered.models[0].settings;
        if let Some(Value::Object(settings_map)) = model_settings {
            assert!(settings_map.contains_key("temperature"));
            assert!(settings_map.contains_key("max_tokens"));
            // 确保没有敏感字段（在这个简单的例子中，
            // ModelSettings本身不包含敏感字段）
        }
    }

    #[test]
    fn test_is_sensitive_field() {
        assert!(FieldFilter::is_sensitive_field("api_key"));
        assert!(FieldFilter::is_sensitive_field("secret"));
        assert!(FieldFilter::is_sensitive_field("password"));
        assert!(!FieldFilter::is_sensitive_field("name"));
        assert!(!FieldFilter::is_sensitive_field("description"));
    }

    #[test]
    fn test_is_protected_field() {
        assert!(FieldFilter::is_protected_field("builtin"));
        assert!(FieldFilter::is_protected_field("created"));
        assert!(FieldFilter::is_protected_field("id"));
        assert!(!FieldFilter::is_protected_field("name"));
        assert!(!FieldFilter::is_protected_field("api_key"));
    }

    #[test]
    fn test_get_updatable_fields_regular_provider() {
        let fields = FieldFilter::get_updatable_fields(false);
        assert!(fields.contains("name"));
        assert!(fields.contains("api_key"));
        assert!(fields.contains("enabled"));
        assert!(!fields.contains("builtin"));
        assert!(!fields.contains("created"));
    }

    #[test]
    fn test_get_updatable_fields_builtin_provider() {
        let fields = FieldFilter::get_updatable_fields(true);
        assert!(!fields.contains("name")); // 内置提供商不能修改name
        assert!(fields.contains("api_key"));
        assert!(fields.contains("enabled"));
        assert!(!fields.contains("builtin"));
        assert!(!fields.contains("created"));
    }

    #[test]
    fn test_filter_update_fields() {
        let update_data = json!({
            "name": "New Name",
            "api_key": "new-key",
            "builtin": true,
            "created": "2024-01-01T00:00:00Z",
            "enabled": false
        });

        let (filtered, removed_fields) = FieldFilter::filter_update_fields(update_data, false);

        if let Value::Object(map) = filtered {
            assert!(map.contains_key("name"));
            assert!(map.contains_key("api_key"));
            assert!(map.contains_key("enabled"));
            assert!(!map.contains_key("builtin"));
            assert!(!map.contains_key("created"));
        }

        assert!(removed_fields.contains(&"builtin".to_string()));
        assert!(removed_fields.contains(&"created".to_string()));
    }

    #[test]
    fn test_filter_update_fields_builtin_provider() {
        let update_data = json!({
            "name": "New Name",
            "api_key": "new-key",
            "enabled": false
        });

        let (filtered, removed_fields) = FieldFilter::filter_update_fields(update_data, true);

        if let Value::Object(map) = filtered {
            assert!(!map.contains_key("name")); // 内置提供商不能修改name
            assert!(map.contains_key("api_key"));
            assert!(map.contains_key("enabled"));
        }

        assert!(removed_fields.contains(&"name".to_string()));
    }
}
