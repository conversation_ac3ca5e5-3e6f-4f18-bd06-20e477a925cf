use std::sync::Arc;

use dotenvy::dotenv;
use jsonwebtoken::{decode, DecodingKey, Validation};
use tracing::{debug, error, warn};

use crate::{
    auth::{
        jwt_cache::JwtCache,
        token_blacklist::TokenBlacklist,
        user_claims::{AuthType, UserClaims, UserContext},
    },
    error::error::CocoError,
    services::token_service::TokenService,
};

/// Token验证器 - 负责JWT和API Token的验证
#[derive(Clone)]
pub struct TokenValidator {
    /// JWT缓存
    jwt_cache: Arc<JwtCache>,
    /// Token黑名单
    token_blacklist: Arc<TokenBlacklist>,
    /// Token服务
    token_service: Arc<TokenService>,
    /// JWT密钥
    jwt_secret: String,
}

/// Token验证结果
#[derive(Debug)]
pub struct TokenValidationResult {
    /// 用户上下文
    pub user_context: UserContext,
    /// 是否需要权限检查
    pub requires_permission_check: bool,
}

impl TokenValidator {
    /// 创建新的Token验证器
    pub fn new(
        jwt_cache: Arc<JwtCache>,
        token_blacklist: Arc<TokenBlacklist>,
        token_service: Arc<TokenService>,
        jwt_secret: Option<String>,
    ) -> Result<Self, CocoError> {
        let jwt_secret = if let Some(secret) = jwt_secret {
            secret
        } else {
            // 尝试加载 .env 文件（如果存在）
            let _ = dotenv();

            std::env::var("JWT_SECRET").map_err(|_| {
                error!("JWT_SECRET environment variable is required but not set");
                CocoError::config(
                    "JWT_SECRET environment variable is required for security. Please set it in \
                     your environment or .env file.",
                )
            })?
        };

        Ok(Self {
            jwt_cache,
            token_blacklist,
            token_service,
            jwt_secret,
        })
    }

    /// 验证Token并返回用户上下文
    pub async fn validate_token(
        &self,
        token: &str,
        auth_type: AuthType,
    ) -> Result<TokenValidationResult, CocoError> {
        match auth_type {
            AuthType::JWT => self.validate_jwt_token(token).await,
            AuthType::ApiToken => self.validate_api_token(token).await,
        }
    }

    /// 验证JWT Token
    async fn validate_jwt_token(&self, token: &str) -> Result<TokenValidationResult, CocoError> {
        debug!("验证JWT Token: {}***", &token[..token.len().min(8)]);

        // 检查Token是否在黑名单中
        if self.token_blacklist.is_blacklisted(token).await {
            return Err(CocoError::unauthorized("JWT令牌已被注销"));
        }

        // 尝试从缓存获取验证结果
        if let Some(cached_claims) = self.jwt_cache.get(token) {
            if !cached_claims.is_expired() {
                debug!("JWT Token缓存命中");
                let user_context =
                    UserContext::from_jwt_claims(&cached_claims, Some(token.to_string()));
                return Ok(TokenValidationResult {
                    user_context,
                    requires_permission_check: true,
                });
            } else {
                // JWT已过期，从缓存中移除
                self.jwt_cache.remove(token);
            }
        }

        // 缓存未命中或已过期，进行完整验证
        let validation = Validation::default();
        match decode::<UserClaims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_ref()),
            &validation,
        ) {
            Ok(token_data) => {
                let claims = token_data.claims;

                // 检查令牌是否过期
                if claims.is_expired() {
                    return Err(CocoError::unauthorized("JWT令牌已过期"));
                }

                // 将验证结果缓存
                self.jwt_cache.put(token, claims.clone());

                debug!("JWT Token验证成功: user_id={}", claims.user_id);
                let user_context = UserContext::from_jwt_claims(&claims, Some(token.to_string()));
                Ok(TokenValidationResult {
                    user_context,
                    requires_permission_check: true,
                })
            }
            Err(e) => {
                error!("JWT验证失败: {}", e);
                Err(CocoError::unauthorized("无效的JWT令牌"))
            }
        }
    }

    /// 验证API Token
    async fn validate_api_token(&self, token: &str) -> Result<TokenValidationResult, CocoError> {
        debug!("验证API Token: {}***", &token[..token.len().min(8)]);

        match self.token_service.validate_api_token(token).await {
            Ok(access_token) => {
                // 更新令牌最后使用时间
                if let Err(e) = self.token_service.update_last_used(token).await {
                    warn!("更新Token最后使用时间失败: {}", e);
                }

                debug!("API Token验证成功: user_id={}", access_token.user_id);
                let user_context = UserContext::from_api_token(
                    access_token.user_id.clone(),
                    access_token.user_id.clone(), // 使用user_id作为username
                    access_token.roles,
                    access_token.provider,
                    Some(token.to_string()),
                );

                Ok(TokenValidationResult {
                    user_context,
                    requires_permission_check: true,
                })
            }
            Err(e) => {
                error!("API Token验证失败: {}", e);
                Err(CocoError::unauthorized("无效的API令牌"))
            }
        }
    }

    /// 检查用户是否有指定权限
    pub fn check_permission(&self, user_context: &UserContext, permission: &str) -> bool {
        // 管理员拥有所有权限
        if user_context.is_admin() {
            debug!("管理员用户 {} 拥有所有权限", user_context.username);
            return true;
        }

        // 检查用户角色是否包含所需权限
        let has_permission = match permission {
            // Model Provider API权限
            "createLLMPermission" => {
                user_context.has_role("llm_admin") || user_context.has_role("llm_creator")
            }
            "readLLMPermission" => {
                user_context.has_role("llm_admin")
                    || user_context.has_role("llm_creator")
                    || user_context.has_role("llm_reader")
            }
            "updateLLMPermission" => {
                user_context.has_role("llm_admin") || user_context.has_role("llm_creator")
            }
            "deleteLLMPermission" => user_context.has_role("llm_admin"),
            "searchLLMPermission" => {
                user_context.has_role("llm_admin")
                    || user_context.has_role("llm_creator")
                    || user_context.has_role("llm_reader")
            }
            // 通用权限
            _ => {
                warn!("未知权限: {}", permission);
                false
            }
        };

        debug!(
            "用户 {} 权限检查 {}: {}",
            user_context.username,
            permission,
            if has_permission { "通过" } else { "拒绝" }
        );

        has_permission
    }

    /// 从请求头中提取认证信息
    pub fn extract_auth_info(headers: &axum::http::HeaderMap) -> Option<(String, AuthType)> {
        // 优先检查 Authorization: Bearer 头
        if let Some(auth_header) = headers.get("Authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if auth_str.starts_with("Bearer ") {
                    let token = auth_str[7..].to_string(); // 移除"Bearer "前缀
                    return Some((token, AuthType::JWT));
                }
            }
        }

        // 检查 X-API-TOKEN 头
        if let Some(api_token) = headers.get("X-API-TOKEN") {
            if let Ok(token_str) = api_token.to_str() {
                return Some((token_str.to_string(), AuthType::ApiToken));
            }
        }

        None
    }

    /// 检查路径是否需要跳过认证
    pub fn should_skip_auth(path: &str) -> bool {
        // Model Provider API的公开端点
        const SKIP_AUTH_PATHS: &[&str] = &[
            "/health",            // 健康检查
            "/setup/_initialize", // 系统初始化
            "/account/login",     // 用户登录
            "/sso/login/cloud",   // SSO登录
        ];

        SKIP_AUTH_PATHS.contains(&path)
    }

    /// 遮蔽Token用于日志记录
    pub fn mask_token(token: &str) -> String {
        if token.len() <= 4 {
            "*".repeat(token.len())
        } else {
            let prefix = &token[..2];
            let suffix = &token[token.len() - 2..];
            format!("{}***{}", prefix, suffix)
        }
    }
}

/// Model Provider API权限常量
pub mod permissions {
    /// 创建模型提供商权限
    pub const CREATE_LLM: &str = "createLLMPermission";
    /// 读取模型提供商权限
    pub const READ_LLM: &str = "readLLMPermission";
    /// 更新模型提供商权限
    pub const UPDATE_LLM: &str = "updateLLMPermission";
    /// 删除模型提供商权限
    pub const DELETE_LLM: &str = "deleteLLMPermission";
    /// 搜索模型提供商权限
    pub const SEARCH_LLM: &str = "searchLLMPermission";
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_auth_info() {
        use axum::http::HeaderMap;

        // 测试Bearer Token提取
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", "Bearer test-jwt-token".parse().unwrap());

        let result = TokenValidator::extract_auth_info(&headers);
        assert!(result.is_some());
        let (token, auth_type) = result.unwrap();
        assert_eq!(token, "test-jwt-token");
        assert_eq!(auth_type, AuthType::JWT);

        // 测试API Token提取
        let mut headers = HeaderMap::new();
        headers.insert("X-API-TOKEN", "test-api-token".parse().unwrap());

        let result = TokenValidator::extract_auth_info(&headers);
        assert!(result.is_some());
        let (token, auth_type) = result.unwrap();
        assert_eq!(token, "test-api-token");
        assert_eq!(auth_type, AuthType::ApiToken);

        // 测试无认证信息
        let headers = HeaderMap::new();
        let result = TokenValidator::extract_auth_info(&headers);
        assert!(result.is_none());
    }

    #[test]
    fn test_should_skip_auth() {
        assert!(TokenValidator::should_skip_auth("/health"));
        assert!(TokenValidator::should_skip_auth("/setup/_initialize"));
        assert!(TokenValidator::should_skip_auth("/account/login"));
        assert!(TokenValidator::should_skip_auth("/sso/login/cloud"));
        assert!(!TokenValidator::should_skip_auth("/model_provider/"));
        assert!(!TokenValidator::should_skip_auth("/api/test"));
    }

    #[test]
    fn test_mask_token() {
        assert_eq!(TokenValidator::mask_token("abc"), "***");
        assert_eq!(TokenValidator::mask_token("abcd"), "****");
        assert_eq!(TokenValidator::mask_token("abcdef"), "ab***ef");
        assert_eq!(TokenValidator::mask_token("test-token-123"), "te***23");
        assert_eq!(TokenValidator::mask_token(""), "");
    }

    #[test]
    fn test_check_permission() {
        use crate::{
            auth::{jwt_cache::JwtCache, token_blacklist::TokenBlacklist},
            repositories::token_repository::TokenRepository,
            services::token_service::TokenService,
        };

        // 创建测试用的TokenValidator
        let jwt_cache = Arc::new(JwtCache::default());
        let token_blacklist = Arc::new(TokenBlacklist::new());
        let token_repository = Arc::new(TokenRepository::new_with_global_db());
        let token_service = Arc::new(TokenService::new(token_repository));
        let validator = TokenValidator::new(
            jwt_cache,
            token_blacklist,
            token_service,
            Some("test-secret".to_string()),
        )
        .unwrap();

        // 测试管理员权限
        let admin_context = UserContext::from_api_token(
            "admin".to_string(),
            "admin".to_string(),
            vec!["admin".to_string()],
            "test".to_string(),
            None,
        );
        assert!(validator.check_permission(&admin_context, permissions::CREATE_LLM));
        assert!(validator.check_permission(&admin_context, permissions::DELETE_LLM));

        // 测试LLM管理员权限
        let llm_admin_context = UserContext::from_api_token(
            "llm_admin".to_string(),
            "llm_admin".to_string(),
            vec!["llm_admin".to_string()],
            "test".to_string(),
            None,
        );
        assert!(validator.check_permission(&llm_admin_context, permissions::CREATE_LLM));
        assert!(validator.check_permission(&llm_admin_context, permissions::DELETE_LLM));

        // 测试LLM读取者权限
        let llm_reader_context = UserContext::from_api_token(
            "llm_reader".to_string(),
            "llm_reader".to_string(),
            vec!["llm_reader".to_string()],
            "test".to_string(),
            None,
        );
        assert!(validator.check_permission(&llm_reader_context, permissions::READ_LLM));
        assert!(!validator.check_permission(&llm_reader_context, permissions::CREATE_LLM));
        assert!(!validator.check_permission(&llm_reader_context, permissions::DELETE_LLM));
    }
}
