use axum::{
    body::Body,
    extract::State,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use tracing::{error, info, warn};

use crate::{
    app_state::AppState,
    auth::{token_validator::TokenValidator, user_claims::UserContext},
};

/// Model Provider API认证中间件
/// 支持JWT和API令牌认证，并传递用户上下文
pub async fn auth_middleware(
    State(app_state): State<AppState>,
    mut request: Request<Body>,
    next: Next,
) -> Result<Response, impl IntoResponse> {
    info!("Processing authentication for request to {}", request.uri());

    // 检查是否需要跳过身份验证的路径
    let path = request.uri().path().to_string();
    if TokenValidator::should_skip_auth(&path) {
        info!("Skipping authentication for path: {}", path);
        let response = next.run(request).await;
        info!("Response from handler for path: {}", path);
        return Ok(response);
    }

    // 创建Token验证器
    let token_validator = match TokenValidator::new(
        app_state.jwt_cache.clone(),
        app_state.token_blacklist.clone(),
        app_state.token_service.clone(),
        None, // 使用环境变量中的JWT密钥
    ) {
        Ok(validator) => validator,
        Err(e) => {
            error!("Failed to create token validator: {}", e);
            return Ok((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(serde_json::json!({
                    "error": "Authentication service unavailable",
                    "message": "JWT_SECRET environment variable is required"
                })),
            )
                .into_response());
        }
    };

    // 从请求头中提取认证信息
    let auth_info = TokenValidator::extract_auth_info(request.headers());

    match auth_info {
        Some((token, auth_type)) => {
            // 验证令牌并获取用户上下文
            match token_validator.validate_token(&token, auth_type).await {
                Ok(validation_result) => {
                    info!(
                        "Authentication successful for user: {} ({})",
                        validation_result.user_context.username,
                        TokenValidator::mask_token(&token)
                    );

                    // 将用户上下文添加到请求扩展中
                    request
                        .extensions_mut()
                        .insert(validation_result.user_context);

                    Ok(next.run(request).await)
                }
                Err(auth_error) => {
                    warn!("Authentication failed: {}", auth_error);
                    let error_response = create_auth_error_response(&auth_error.to_string());
                    Err((StatusCode::UNAUTHORIZED, Json(error_response)))
                }
            }
        }
        None => {
            info!("Authentication failed: missing token");
            let error_response = json!({
                "error": "Missing authentication",
                "message": "缺少认证信息。请在请求头中提供Authorization: Bearer <token>或X-API-TOKEN: <token>。"
            });
            Err((StatusCode::UNAUTHORIZED, Json(error_response)))
        }
    }
}

/// 权限检查中间件
/// 检查用户是否有指定权限
pub async fn check_permission_middleware(
    permission: String,
    request: Request<Body>,
    next: Next,
) -> Result<Response, impl IntoResponse> {
    // 从请求扩展中获取用户上下文
    if let Some(user_context) = request.extensions().get::<UserContext>() {
        // 创建Token验证器进行权限检查
        let token_validator = match TokenValidator::new(
            std::sync::Arc::new(crate::auth::jwt_cache::JwtCache::default()),
            std::sync::Arc::new(crate::auth::token_blacklist::TokenBlacklist::new()),
            std::sync::Arc::new(crate::services::token_service::TokenService::new(
                std::sync::Arc::new(
                    crate::repositories::token_repository::TokenRepository::new_with_global_db(),
                ),
            )),
            None,
        ) {
            Ok(validator) => validator,
            Err(e) => {
                error!(
                    "Failed to create token validator for permission check: {}",
                    e
                );
                return Ok((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(json!({
                        "error": "Permission check service unavailable",
                        "message": "JWT_SECRET environment variable is required"
                    })),
                )
                    .into_response());
            }
        };

        if token_validator.check_permission(user_context, &permission) {
            Ok(next.run(request).await)
        } else {
            let error_response = json!({
                "error": "Insufficient permissions",
                "message": format!("用户缺少权限: {}", permission)
            });
            Err((StatusCode::FORBIDDEN, Json(error_response)))
        }
    } else {
        // 没有用户上下文，说明认证失败
        let error_response = json!({
            "error": "Authentication required",
            "message": "需要认证才能访问此资源"
        });
        Err((StatusCode::UNAUTHORIZED, Json(error_response)))
    }
}

/// 创建认证错误响应
fn create_auth_error_response(error_msg: &str) -> serde_json::Value {
    json!({
        "error": "Invalid authentication token",
        "message": error_msg
    })
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use axum::{
        body::Body,
        extract::Extension,
        http::{Request, StatusCode},
        middleware::from_fn_with_state,
        response::Json,
        routing::get,
        Router,
    };
    use tower::util::ServiceExt;

    use super::*;
    use crate::{
        auth::user_claims::AuthType, config::config_manager::ConfigManager,
        services::token_service::TokenService,
    };

    // 创建测试用的AppState
    async fn create_test_app_state() -> AppState {
        use crate::{
            database::{DatabaseConfig, SurrealDBClient},
            repositories::token_repository::TokenRepository,
        };

        let config_manager = Arc::new(ConfigManager::new().unwrap());

        // 注意：这需要运行中的SurrealDB实例
        let db_config = DatabaseConfig::default();
        let db_client = Arc::new(SurrealDBClient::new(db_config).await.unwrap());
        let token_repository = Arc::new(TokenRepository::new_with_global_db());
        let token_service = Arc::new(TokenService::new(token_repository.clone()));

        AppState::new(
            config_manager,
            db_client,
            token_repository,
            token_service,
            Arc::new(crate::auth::token_blacklist::TokenBlacklist::new()),
            Arc::new(crate::auth::jwt_cache::JwtCache::default()),
        )
    }

    // 测试处理器，用于验证用户上下文是否正确传递
    async fn test_handler_with_context(
        Extension(user_context): Extension<UserContext>,
    ) -> Json<serde_json::Value> {
        Json(json!({
            "user_id": user_context.user_id,
            "username": user_context.username,
            "auth_type": match user_context.auth_type {
                AuthType::JWT => "JWT",
                AuthType::ApiToken => "ApiToken",
            }
        }))
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_auth_middleware_allows_skip_paths() {
        let app_state = create_test_app_state().await;
        let app = Router::new()
            .route("/setup/_initialize", get(|| async { "ok" }))
            .route("/health", get(|| async { "ok" }))
            .route("/account/login", get(|| async { "ok" }))
            .route("/sso/login/cloud", get(|| async { "ok" }))
            .route_layer(from_fn_with_state(app_state, auth_middleware));

        // 测试所有跳过认证的路径
        let paths = [
            "/setup/_initialize",
            "/health",
            "/account/login",
            "/sso/login/cloud",
        ];

        for path in paths {
            let request = Request::builder().uri(path).body(Body::empty()).unwrap();

            let response = app.clone().oneshot(request).await.unwrap();
            assert_eq!(
                response.status(),
                StatusCode::OK,
                "Path {} should be allowed",
                path
            );
        }
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_auth_middleware_blocks_without_token() {
        let app_state = create_test_app_state().await;
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn_with_state(app_state, auth_middleware));

        let request = Request::builder().uri("/test").body(Body::empty()).unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_extract_auth_info() {
        use axum::http::HeaderMap;

        use crate::auth::user_claims::AuthType;

        // 测试Bearer令牌提取
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", "Bearer test-jwt-token".parse().unwrap());

        let result = TokenValidator::extract_auth_info(&headers);
        assert!(result.is_some());
        let (token, auth_type) = result.unwrap();
        assert_eq!(token, "test-jwt-token");
        assert_eq!(auth_type, AuthType::JWT);

        // 测试API令牌提取
        let mut headers = HeaderMap::new();
        headers.insert("X-API-TOKEN", "test-api-token".parse().unwrap());

        let result = TokenValidator::extract_auth_info(&headers);
        assert!(result.is_some());
        let (token, auth_type) = result.unwrap();
        assert_eq!(token, "test-api-token");
        assert_eq!(auth_type, AuthType::ApiToken);

        // 测试无认证信息
        let headers = HeaderMap::new();
        let result = TokenValidator::extract_auth_info(&headers);
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_should_skip_auth() {
        assert!(TokenValidator::should_skip_auth("/setup/_initialize"));
        assert!(TokenValidator::should_skip_auth("/health"));
        assert!(TokenValidator::should_skip_auth("/account/login"));
        assert!(TokenValidator::should_skip_auth("/sso/login/cloud"));
        assert!(!TokenValidator::should_skip_auth("/api/test"));
        assert!(!TokenValidator::should_skip_auth("/account/profile"));
        assert!(!TokenValidator::should_skip_auth(
            "/auth/request_access_token"
        ));
    }

    #[tokio::test]
    async fn test_mask_token() {
        assert_eq!(TokenValidator::mask_token("abc"), "***");
        assert_eq!(TokenValidator::mask_token("abcd"), "****");
        assert_eq!(TokenValidator::mask_token("abcdef"), "ab***ef");
        assert_eq!(TokenValidator::mask_token("test-token-123"), "te***23");
        assert_eq!(TokenValidator::mask_token(""), "");
    }

    #[tokio::test]
    async fn test_create_auth_error_response() {
        let response = create_auth_error_response("测试错误消息");
        assert_eq!(response["error"], "Invalid authentication token");
        assert_eq!(response["message"], "测试错误消息");
    }
}
