# Coco Server Rust 环境变量配置示例
# 复制此文件为 .env 并设置实际值

# JWT 配置 (必需)
# 生产环境中请使用强随机密钥，至少32字符
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 数据库配置
DATABASE_URL=ws://127.0.0.1:8000
DATABASE_USERNAME=root
DATABASE_PASSWORD=root
DATABASE_NAMESPACE=coco
DATABASE_NAME=coco

# 日志配置
RUST_LOG=info
LOG_LEVEL=info

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=10000

# API 令牌配置（可选）
API_TOKEN=your-api-token-here

# 开发模式配置（可选）
DEVELOPMENT_MODE=true
