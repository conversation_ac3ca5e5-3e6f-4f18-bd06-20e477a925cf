# JWT 安全升级指南

## 概述

为了提高安全性，我们已经移除了硬编码的JWT密钥，现在要求通过环境变量 `JWT_SECRET` 来设置JWT密钥。

## 重要变更

### ⚠️ 破坏性变更
- **移除了硬编码的JWT密钥**
- **现在必须设置 `JWT_SECRET` 环境变量**
- **如果未设置环境变量，服务器将无法启动**

### 🔧 修复的安全问题
1. **硬编码JWT密钥** - 移除了代码中的硬编码密钥
2. **密码明文日志** - 移除了可能泄露密码的日志记录
3. **配置文件中的硬编码密钥** - 更新了配置文件

## 快速开始

### 1. 生成安全的JWT密钥

使用提供的脚本生成安全的JWT密钥：

```bash
./scripts/generate_jwt_secret.sh
```

### 2. 设置环境变量

#### 方法一：使用 .env 文件（推荐）

```bash
# 复制示例文件
cp .env.example .env

# 编辑 .env 文件，设置您的JWT密钥
# JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
```

#### 方法二：直接设置环境变量

```bash
export JWT_SECRET="your-super-secret-jwt-key-here-change-in-production"
```

### 3. 启动服务器

```bash
cargo run
```

## 生产环境部署

### 安全建议

1. **使用强随机密钥**：至少32字符的随机字符串
2. **定期轮换密钥**：建议每3-6个月更换一次
3. **安全存储**：不要将密钥提交到版本控制系统
4. **环境隔离**：不同环境使用不同的密钥

### 密钥生成示例

```bash
# 使用 openssl 生成64字符的随机密钥
openssl rand -base64 48

# 使用 uuidgen 生成（较短但仍安全）
uuidgen | tr -d '-'
```

### Docker 部署

```dockerfile
# Dockerfile
ENV JWT_SECRET=""

# 或在 docker-compose.yml 中
environment:
  - JWT_SECRET=${JWT_SECRET}
```

### Kubernetes 部署

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: coco-server-secrets
data:
  jwt-secret: <base64-encoded-secret>
---
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: coco-server
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: coco-server-secrets
              key: jwt-secret
```

## 故障排除

### 常见错误

1. **"JWT_SECRET environment variable is required"**
   - 解决方案：设置 `JWT_SECRET` 环境变量

2. **"Authentication service unavailable"**
   - 解决方案：检查环境变量是否正确设置

3. **令牌验证失败**
   - 解决方案：确保所有服务实例使用相同的JWT密钥

### 验证配置

```bash
# 检查环境变量是否设置
echo $JWT_SECRET

# 测试服务器启动
cargo run --bin coco-server
```

## 迁移指南

### 从旧版本升级

1. **备份现有数据**
2. **生成新的JWT密钥**
3. **设置环境变量**
4. **重启服务器**
5. **验证功能正常**

### 注意事项

- 更换JWT密钥后，所有现有的JWT令牌将失效
- 用户需要重新登录
- API令牌不受影响

## 开发环境

### 测试配置

```bash
# 设置测试用的JWT密钥
export JWT_SECRET="test-secret-key-for-development-only"

# 运行测试
cargo test
```

### 调试

启用调试日志来查看JWT相关信息：

```bash
RUST_LOG=debug cargo run
```

## 相关文件

- `.env.example` - 环境变量示例文件
- `scripts/generate_jwt_secret.sh` - JWT密钥生成脚本
- `config/auth.yml` - 认证配置文件
- `src/handlers/account_handler.rs` - 账户处理器
- `src/auth/token_validator.rs` - 令牌验证器
