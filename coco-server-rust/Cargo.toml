[package]
name = "coco-server"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = { version = "0.8.4", features = ["macros", "ws"] }
tower = { version = "0.5.2", features = ["util"] }
tower-http = { version = "0.6.6", features = ["cors", "trace", "timeout"] }

# 异步运行时
tokio = { version = "1", features = ["full"] }

# 数据库
surrealdb = { version = "2.3.7", features = [
    "kv-rocksdb",
    "scripting",
    "protocol-ws",
] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# 错误处理
thiserror = "2.0.12"
anyhow = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.17.0", features = ["v4", "serde"] }

# 配置管理
config = "0.15.13"
toml = "0.9.4"
notify = "8.2.0"
dotenvy = "0.15"

# 验证
validator = { version = "0.20.0", features = ["derive"] }
url = "2.5.4"

# 其他依赖
bytes = "1.10.1"
futures = "0.3"
tokio-rustls = "0.26.2"
rustls-pemfile = "2.2.0"
reqwest = { version = "0.12.22", features = ["json"] }
bcrypt = "0.17.0"
jsonwebtoken = "9.3"
urlencoding = "2.1.3"
dashmap = "6.1.0"
regex = "1.11.1"
tokio-stream = "0.1"
async-trait = "0.1"
rand = "0.9.2"
sha2 = "0.10"

[dev-dependencies]
# 测试框架
tokio-test = "0.4"
mockall = "0.13.1"

# 性能测试
criterion = { version = "0.7.0", features = ["html_reports"] }

# 其他测试依赖
tokio-tungstenite = "0.27.0"
tempfile = "3.20.0"
http-body-util = "0.1"
# 使用与axum兼容的hyper版本
hyper = { version = "1", features = ["full"] }

[[bench]]
name = "auth_performance"
harness = false
